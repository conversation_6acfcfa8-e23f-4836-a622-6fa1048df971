<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Test - Terminal Portfolio</title>
    <style>
        body {
            font-family: monospace;
            background: #000;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .test-section {
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-button {
            background: #333;
            color: #00ff00;
            border: 1px solid #00ff00;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
            font-family: monospace;
        }
        .test-button:hover {
            background: #00ff00;
            color: #000;
        }
        .log {
            background: #111;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            max-height: 200px;
            overflow-y: auto;
        }
        .hidden-input {
            position: absolute;
            left: -9999px;
            opacity: 0;
        }
        .status {
            color: #ff6b35;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Mobile Terminal Portfolio Test</h1>
    
    <div class="test-section">
        <h3>Device Detection</h3>
        <div id="device-info"></div>
    </div>
    
    <div class="test-section">
        <h3>Touch Events Test</h3>
        <button class="test-button" id="touch-test">Tap to Test Touch Events</button>
        <div id="touch-log"></div>
    </div>
    
    <div class="test-section">
        <h3>Mobile Keyboard Test</h3>
        <button class="test-button" id="keyboard-test">Focus Hidden Input</button>
        <input type="text" id="hidden-input" class="hidden-input" placeholder="Hidden input">
        <div id="keyboard-log"></div>
    </div>
    
    <div class="test-section">
        <h3>Terminal Portfolio Test</h3>
        <button class="test-button" onclick="window.location.href='index.html'">Open Terminal Portfolio</button>
        <button class="test-button" onclick="testTerminalMobile()">Test Mobile Features</button>
    </div>
    
    <div class="test-section">
        <h3>Debug Log</h3>
        <div class="log" id="debug-log"></div>
        <button class="test-button" onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        function log(message) {
            const debugLog = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}<br>`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        // Device detection
        function detectDevice() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const deviceInfo = document.getElementById('device-info');
            
            deviceInfo.innerHTML = `
                <div class="status">Mobile Device: ${isMobile ? 'YES' : 'NO'}</div>
                <div>User Agent: ${navigator.userAgent}</div>
                <div>Screen Size: ${window.screen.width}x${window.screen.height}</div>
                <div>Viewport: ${window.innerWidth}x${window.innerHeight}</div>
                <div>Touch Support: ${'ontouchstart' in window ? 'YES' : 'NO'}</div>
            `;
            
            log(`Device detection complete. Mobile: ${isMobile}`);
        }

        // Touch events test
        function setupTouchTest() {
            const touchButton = document.getElementById('touch-test');
            const touchLog = document.getElementById('touch-log');
            
            touchButton.addEventListener('touchstart', (e) => {
                touchLog.innerHTML += 'TouchStart detected<br>';
                log('TouchStart event fired');
            });
            
            touchButton.addEventListener('touchend', (e) => {
                touchLog.innerHTML += 'TouchEnd detected<br>';
                log('TouchEnd event fired');
            });
            
            touchButton.addEventListener('click', (e) => {
                touchLog.innerHTML += 'Click detected<br>';
                log('Click event fired');
            });
        }

        // Keyboard test
        function setupKeyboardTest() {
            const keyboardButton = document.getElementById('keyboard-test');
            const hiddenInput = document.getElementById('hidden-input');
            const keyboardLog = document.getElementById('keyboard-log');
            
            keyboardButton.addEventListener('click', () => {
                hiddenInput.focus();
                log('Attempting to focus hidden input');
            });
            
            hiddenInput.addEventListener('focus', () => {
                keyboardLog.innerHTML += 'Hidden input focused<br>';
                log('Hidden input focused successfully');
            });
            
            hiddenInput.addEventListener('blur', () => {
                keyboardLog.innerHTML += 'Hidden input blurred<br>';
                log('Hidden input lost focus');
            });
            
            hiddenInput.addEventListener('input', (e) => {
                keyboardLog.innerHTML += `Input: ${e.target.value}<br>`;
                log(`Input received: ${e.target.value}`);
            });
        }

        // Test terminal mobile features
        function testTerminalMobile() {
            log('Testing terminal mobile features...');
            
            // Simulate mobile environment
            if (typeof window.terminal !== 'undefined') {
                log('Terminal object found');
                if (window.terminal.mobileInput) {
                    log('Mobile input element exists');
                    window.terminal.mobileInput.focus();
                    log('Attempted to focus mobile input');
                } else {
                    log('Mobile input element not found');
                }
            } else {
                log('Terminal object not found - open terminal portfolio first');
            }
        }

        // Initialize tests
        document.addEventListener('DOMContentLoaded', () => {
            log('Mobile test page loaded');
            detectDevice();
            setupTouchTest();
            setupKeyboardTest();
        });

        // Global error handler
        window.addEventListener('error', (e) => {
            log(`Error: ${e.message} at ${e.filename}:${e.lineno}`);
        });
    </script>
</body>
</html>

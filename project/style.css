* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Courier New', monospace;
    background: #000;
    color: #00ff00;
    overflow-x: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.terminal-container {
    width: 100%;
    max-width: 1000px;
    height: 80vh;
    min-height: 600px;
    background: #000;
    border: 2px solid #333;
    border-radius: 8px;
    box-shadow: 0 0 30px rgba(0, 255, 0, 0.3);
    display: flex;
    flex-direction: column;
    position: relative;
}

.terminal-header {
    background: #1a1a1a;
    padding: 10px 15px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #333;
    border-radius: 6px 6px 0 0;
}

.terminal-buttons {
    display: flex;
    gap: 8px;
}

.btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.btn.close {
    background: #ff5f56;
}

.btn.minimize {
    background: #ffbd2e;
}

.btn.maximize {
    background: #27ca3f;
}

.terminal-title {
    flex: 1;
    text-align: center;
    color: #ccc;
    font-size: 14px;
}

.terminal-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    font-size: 16px;
    line-height: 1.4;
    background: #000;
}

.prompt {
    color: #00ff00;
    text-shadow: 0 0 5px #00ff00;
}

.cursor {
    animation: blink 1s infinite;
    background: #00ff00;
    color: #000;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.input-line {
    display: flex;
    align-items: center;
    margin-top: 10px;
}

#command-input {
    color: #fff;
    min-width: 1px;
}

.ascii-art {
    color: #00ff00;
    text-shadow: 0 0 10px #00ff00;
    margin-bottom: 20px;
    font-size: 12px;
    line-height: 1.2;
}

.output-line {
    margin: 5px 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.command-line {
    color: #00ff00;
    text-shadow: 0 0 5px #00ff00;
}

.error {
    color: #ff6b6b;
}

.success {
    color: #51cf66;
}

.info {
    color: #74c0fc;
}

.link {
    color: #ffd43b;
    text-decoration: underline;
    cursor: pointer;
}

.link:hover {
    color: #fff;
    text-shadow: 0 0 5px #ffd43b;
}

.command-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 15px 20px;
    background: #111;
    border-top: 1px solid #333;
    border-radius: 0 0 6px 6px;
}

.command-buttons button {
    background: transparent;
    border: 1px solid #00ff00;
    color: #00ff00;
    padding: 8px 15px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.command-buttons button:hover {
    background: #00ff00;
    color: #000;
    box-shadow: 0 0 10px #00ff00;
}

/* Scrollbar styling */
.terminal-body::-webkit-scrollbar {
    width: 8px;
}

.terminal-body::-webkit-scrollbar-track {
    background: #111;
}

.terminal-body::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 4px;
}

.terminal-body::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Responsive design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .terminal-container {
        height: 90vh;
        border-radius: 0;
    }
    
    .terminal-header {
        border-radius: 0;
    }
    
    .terminal-body {
        font-size: 14px;
        padding: 15px;
    }
    
    .command-buttons {
        padding: 10px 15px;
        border-radius: 0;
    }
    
    .command-buttons button {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .ascii-art {
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    .terminal-body {
        font-size: 12px;
        padding: 10px;
    }
    
    .command-buttons {
        gap: 5px;
        padding: 8px 10px;
    }
    
    .command-buttons button {
        padding: 5px 10px;
        font-size: 11px;
    }
    
    .terminal-buttons {
        gap: 5px;
    }
    
    .btn {
        width: 10px;
        height: 10px;
    }
    
    .terminal-title {
        font-size: 12px;
    }
    
    .ascii-art {
        font-size: 8px;
    }
}

/* Animation classes */
.typing {
    overflow: hidden;
    border-right: 2px solid #00ff00;
    white-space: nowrap;
    animation: typing 0.05s steps(1, end);
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

.glow {
    text-shadow: 0 0 10px #00ff00, 0 0 20px #00ff00, 0 0 30px #00ff00;
}